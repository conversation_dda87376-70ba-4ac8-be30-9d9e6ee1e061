"use client";

import { useAuthStore } from "@/stores/auth-store";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Loader2, LogOut, Sparkles} from "lucide-react";
import { PortfolioStatusCard } from "@/components/portfolio/PortfolioStatusCard";
import { PortfolioData } from "@/lib/types";
import { getPortfolio, createPortfolioFromTemplate } from "@/lib/portfolio-api";
import { auth } from "@/lib/firebase";
import { signOut } from "firebase/auth";
import { useUniversalExport } from "@/hooks/useUniversalExport";
import { toast } from "sonner";
import { useState, useEffect } from "react";
import { getAllThemes } from "@/themes/theme-registry";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { FullScreenLoader } from "@/components/ui/FullScreenLoader";

// Helper function to delete cookies
const deleteCookie = (name: string) => {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
};

export default function DashboardPage() {
  const { user, isLoaded } = useAuthStore();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { exportPortfolio, isExporting } = useUniversalExport();
  const [isNavigating, setIsNavigating] = useState(false);
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Reset navigation state on unmount or route change
  useEffect(() => {
    return () => {
      setIsNavigating(false);
    };
  }, []);
  const [loadingTemplateId, setLoadingTemplateId] = useState<string | null>(null);

  const { data: portfolio, isLoading } = useQuery<PortfolioData | null>({
    queryKey: ["portfolio", user?.uid],
    queryFn: () => getPortfolio(user!.uid),
    enabled: !!user,
  });

  // Sign out handler
  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut(auth);
      // Clear the query cache on sign out
      await queryClient.clear();
      deleteCookie('firebaseIdToken');
      // Force a reload to ensure clean state
      router.push("/");
    } catch (error) {
      console.error("Sign out error:", error);
      toast.error("Failed to sign out. Please try again.");
    } finally {
      setIsSigningOut(false);
    }
  };

  // Template selection mutation
  const { mutate: selectTemplate } = useMutation({
    mutationFn: createPortfolioFromTemplate,
    onMutate: (variables) => {
      setLoadingTemplateId(variables.templateId);
    },
    onSuccess: (newPortfolio) => {
      queryClient.setQueryData(['portfolio', user?.uid], newPortfolio);
      setIsNavigating(true);
      // Navigate immediately with loading state
      router.push('/portfolio');
    },
    onError: () => {
      setIsNavigating(false);
      setLoadingTemplateId(null);
      toast.error("Failed to create portfolio. Please try again.");
    },
    onSettled: () => {
      setLoadingTemplateId(null);
    }
  });

  // Template selection handler
  const handleSelectTemplate = (templateId: string) => {
    if (!user) {
      toast.error("You must be logged in to select a template.");
      return;
    }
    selectTemplate({
      userId: user.uid,
      userEmail: user.email,
      templateId
    });
  };

  const handlePreview = () => {
    if (!portfolio) return;
    localStorage.setItem('portfolio-preview', JSON.stringify(portfolio));
    window.open('/portfolio/preview', '_blank');
  };


  const handleExport = async () => {
    if (!portfolio) {
      toast.error("No portfolio data available for export.");
      return;
    }

    try {
      await exportPortfolio(portfolio);
      toast.success("Export successful! Check your downloads.");
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast.error(`Export failed: ${error.message}`);
      } else {
        toast.error("Export failed: An unknown error occurred.");
      }
    }
  };

  // Show loading overlay when navigating
  if (isNavigating) {
    return <FullScreenLoader text="Setting up your portfolio editor..." />;
  }

  // Show loading when signing out
  if (isSigningOut) {
    return <FullScreenLoader text="Signing out..." />;
  }

  // Show loading while fetching portfolio data or if auth is not loaded
  if (isLoading || !isLoaded) {
    return <FullScreenLoader text="Loading your dashboard..." />;
  }

  // Show loading if user becomes null (during sign out process) but auth is loaded
  // if (isLoaded && !user) {
  //   return <FullScreenLoader text="Signing out..." />;
  // }

  return (
    <div className="min-h-screen">
      {/* Navigation Bar */}
      <nav className="bg-background border-b ">
        <div className="flex items-center justify-between mx-auto px-8 py-4">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            {/* <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm">P</span>
            </div> */}
            <Link
              href="/"
              className="flex items-center gap-2 relative z-10"
            >
              {/* <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center">
              <span className="text-white font-bold text-sm lg:text-base">
                P
              </span>
            </div> */}
              <Image src="/icon.svg" alt="logo" width={32} height={32} />
              <span className="font-bold text-xl lg:text-2xl gradient-text">
                Profolify
              </span>
            </Link>
          </div>

          {/* User Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                <Avatar className="h-12 w-12 border-2  hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
                  <AvatarFallback>{user?.displayName?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-1 leading-none">
                  <p className="font-medium">{user?.displayName}</p>
                  <p className="w-[200px] truncate text-sm text-muted-foreground">
                    {user?.email}
                  </p>
                </div>
              </div>
              <DropdownMenuSeparator />
              {/* <DropdownMenuItem disabled>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem> */}
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4 hover:text-white" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Scenario A: User has an existing portfolio */}
        {portfolio && (
          <div className="space-y-8">
            {/* Enhanced Welcome Section */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
              <div className="flex items-center space-x-4 mb-4">

                <Avatar className="h-16 w-16 border-2 hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
                  <AvatarFallback>{user?.displayName?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
                <div>
                  <h2 className="text-3xl font-bold text-slate-800">
                    Welcome back, {user?.displayName}!
                  </h2>
                  <p className="text-slate-600 text-lg">
                    Ready to manage your portfolio and showcase your work to the world.
                  </p>
                </div>
              </div>
            </div>

            <PortfolioStatusCard 
              portfolio={portfolio}
              onPreview={handlePreview}
              onExport={handleExport}
              isExporting={isExporting}
            />
          </div>
        )}

        {/* Scenario B: User has no portfolio */}
        {!portfolio && (
          <div className="space-y-8">
            {/* Enhanced Welcome Section for New Users */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100 text-center">
              <div className="flex items-center justify-center space-x-4 mb-6">
                <Avatar className="h-16 w-16 border-2 hover:border-brandPrimary transition-colors">
                  <AvatarImage src={user?.photoURL ?? undefined} alt={user?.displayName || 'User'} />
                  <AvatarFallback>{user?.displayName?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
              </div>
              <h2 className="text-4xl font-bold text-slate-800 mb-4">
                Welcome to Profolify, {user?.displayName}!
              </h2>
              <p className="text-xl text-slate-600 mb-6">
                Let&#39;s create your professional portfolio in minutes
              </p>
              <div className="inline-flex items-center space-x-2 bg-white/60 rounded-full px-4 py-2 text-sm text-slate-600">
                <Sparkles className="w-4 h-4 text-blue-600" />
                <span>Choose a template below to get started</span>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {getAllThemes().map((template) => {
                const isLoadingThisTemplate = loadingTemplateId === template.id;
                return (
                  <Card key={template.id} className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
                    <CardHeader className="p-0">
                      <div className="relative aspect-video w-full overflow-hidden">
                        <Image
                          src={template.preview || '/thumbnails/default-theme.jpg'}
                          alt={template.name}
                          fill
                          className="object-cover transition-transform duration-300"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-2">{template.name}</h3>
                      <p className="text-muted-foreground text-sm mb-4">{template.description}</p>
                      <Button
                        onClick={() => handleSelectTemplate(template.id)}
                        disabled={isLoadingThisTemplate}
                        className="w-full"
                      >
                        {isLoadingThisTemplate ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Setting up...
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-4 w-4" />
                            Use Template
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}